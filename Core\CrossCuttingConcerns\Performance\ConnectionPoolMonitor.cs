using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Performance
{
    /// <summary>
    /// Connection Pool monitoring servisi
    /// 1000+ gym, 100,000+ kullanıcı için connection pool performansını izler
    /// </summary>
    public class ConnectionPoolMonitor
    {
        private readonly ILogger<ConnectionPoolMonitor> _logger;
        private readonly string _connectionString;

        public ConnectionPoolMonitor(ILogger<ConnectionPoolMonitor> logger, string connectionString)
        {
            _logger = logger;
            _connectionString = connectionString;
        }

        /// <summary>
        /// Connection pool istatistiklerini al
        /// </summary>
        public async Task<ConnectionPoolStats> GetConnectionPoolStatsAsync()
        {
            var stats = new ConnectionPoolStats();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    // Connection açma süresi
                    stats.ConnectionOpenTime = stopwatch.ElapsedMilliseconds;
                    
                    // Pool bilgilerini al
                    var poolInfo = await GetPoolInfoAsync(connection);
                    stats.PoolSize = poolInfo.PoolSize;
                    stats.PooledConnections = poolInfo.PooledConnections;
                    stats.NonPooledConnections = poolInfo.NonPooledConnections;
                    
                    stats.IsHealthy = true;
                }
            }
            catch (Exception ex)
            {
                stats.IsHealthy = false;
                stats.ErrorMessage = ex.Message;
                
                _logger.LogError(ex, "Connection pool monitoring hatası: {Error}", ex.Message);
            }
            finally
            {
                stopwatch.Stop();
                stats.TotalCheckTime = stopwatch.ElapsedMilliseconds;
            }

            return stats;
        }

        /// <summary>
        /// Pool bilgilerini SQL Server'dan al
        /// </summary>
        private async Task<PoolInfo> GetPoolInfoAsync(SqlConnection connection)
        {
            var poolInfo = new PoolInfo();

            try
            {
                // SQL Server connection pool bilgilerini al
                var query = @"
                    SELECT 
                        COUNT(*) as TotalConnections
                    FROM sys.dm_exec_sessions 
                    WHERE is_user_process = 1";

                using (var command = new SqlCommand(query, connection))
                {
                    var result = await command.ExecuteScalarAsync();
                    poolInfo.PoolSize = Convert.ToInt32(result);
                }

                // Daha detaylı pool bilgileri için performance counter'ları kullanılabilir
                poolInfo.PooledConnections = poolInfo.PoolSize;
                poolInfo.NonPooledConnections = 0;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Pool bilgileri alınamadı: {Error}", ex.Message);
            }

            return poolInfo;
        }

        /// <summary>
        /// Connection pool sağlık kontrolü
        /// </summary>
        public async Task<bool> IsConnectionPoolHealthyAsync()
        {
            var stats = await GetConnectionPoolStatsAsync();
            
            // Sağlık kriterleri (1000+ gym için)
            var isHealthy = stats.IsHealthy &&
                           stats.ConnectionOpenTime < 1000 && // 1 saniyeden az
                           stats.TotalCheckTime < 2000; // 2 saniyeden az

            if (!isHealthy)
            {
                _logger.LogWarning("Connection pool sağlıksız: {@Stats}", stats);
            }

            return isHealthy;
        }

        /// <summary>
        /// Connection pool performans raporu
        /// </summary>
        public async Task<string> GeneratePerformanceReportAsync()
        {
            var stats = await GetConnectionPoolStatsAsync();
            
            return $@"
=== CONNECTION POOL PERFORMANCE REPORT ===
Sağlık Durumu: {(stats.IsHealthy ? "SAĞLIKLI" : "SORUNLU")}
Connection Açma Süresi: {stats.ConnectionOpenTime}ms
Toplam Kontrol Süresi: {stats.TotalCheckTime}ms
Pool Boyutu: {stats.PoolSize}
Pooled Connections: {stats.PooledConnections}
Non-Pooled Connections: {stats.NonPooledConnections}
Hata Mesajı: {stats.ErrorMessage ?? "Yok"}
Rapor Zamanı: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
==========================================";
        }
    }

    /// <summary>
    /// Connection pool istatistikleri
    /// </summary>
    public class ConnectionPoolStats
    {
        public bool IsHealthy { get; set; }
        public long ConnectionOpenTime { get; set; } // ms
        public long TotalCheckTime { get; set; } // ms
        public int PoolSize { get; set; }
        public int PooledConnections { get; set; }
        public int NonPooledConnections { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Pool bilgileri
    /// </summary>
    public class PoolInfo
    {
        public int PoolSize { get; set; }
        public int PooledConnections { get; set; }
        public int NonPooledConnections { get; set; }
    }
}
