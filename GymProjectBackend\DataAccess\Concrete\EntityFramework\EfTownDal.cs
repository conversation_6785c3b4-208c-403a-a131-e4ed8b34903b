using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfTownDal : EfEntityRepositoryBase<Town, GymContext>, ITownDal
    {
        // Constructor injection (Scalability için)
        public EfTownDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfTownDal() : base()
        {
        }
    }
}
